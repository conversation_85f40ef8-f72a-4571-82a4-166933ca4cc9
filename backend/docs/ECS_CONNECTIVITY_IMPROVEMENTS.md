# ECS Workspace Connectivity Improvements

## Overview

This document outlines the comprehensive improvements made to ensure reliable connectivity between frontend (port 8080) and backend (port 8000) services in ECS workspaces.

## Problem Statement

The original issue was intermittent connectivity between frontend and backend services in ECS containers:
- Frontend (port 8080) sometimes couldn't reach backend (port 8000)
- Health check endpoint (`publicUrl:8000/health`) would sometimes fail
- Services would start but not be accessible from outside the container

## Root Causes Identified

1. **Race Conditions**: Services were marked as "running" before ports were actually accessible
2. **Network Interface Issues**: Services binding to localhost instead of 0.0.0.0
3. **Insufficient Validation**: No comprehensive connectivity testing after service startup
4. **Timing Issues**: Frontend trying to connect to backend before backend was fully ready

## Solutions Implemented

### 1. Enhanced Port Connectivity Validation

**File**: `backend/docker/workspace/server.js`

- Added `waitForPortReady()` function with retry mechanism
- Enhanced `checkPortListening()` with configurable host and timeout
- Added `checkServiceHealth()` for HTTP-based health checks

```javascript
async function waitForPortReady(port, host = 'localhost', maxRetries = 30, retryInterval = 1000)
```

### 2. Improved Health Check Endpoint

**Endpoint**: `GET /health`

Enhanced to include:
- Service connectivity status for each port
- Network interface information
- Cross-service accessibility validation
- Detailed service status with port binding information

### 3. New Connectivity Testing Endpoints

**Endpoint**: `GET /api/connectivity/test`

Comprehensive connectivity testing that:
- Tests frontend port (8080) accessibility
- Tests backend port (8000) accessibility  
- Validates cross-service communication
- Returns detailed test results

**Endpoint**: `POST /api/services/validate`

Pre-startup environment validation:
- Checks workspace directory accessibility
- Validates port availability
- Inspects network interfaces
- Ensures clean startup environment

### 4. Service Startup Improvements

#### Frontend Service
- Added port accessibility validation after startup detection
- Waits for actual port binding before marking as "running"
- Enhanced startup pattern detection

#### Backend Service
- Added connectivity validation after startup
- Ensures port is accessible on both localhost and 0.0.0.0
- Better error handling and logging

### 5. ECS Preview Service Enhancements

**File**: `backend/src/services/ecsPreviewService.ts`

- Pre-startup environment validation
- Post-startup connectivity testing
- Delayed validation to ensure services are fully ready
- Better error reporting and diagnostics

### 6. Frontend Integration

**File**: `frontend/src/components/MonacoWorkspace.tsx`

- Added connectivity testing after service startup
- Delayed iframe loading until connectivity is validated
- Better error handling and user feedback

**File**: `frontend/src/utils/api.ts`

- New `testConnectivity()` API method
- Integration with ECS workspace connectivity testing

### 7. Backend API Endpoints

**File**: `backend/src/controllers/ecsWorkspaceController.ts`

New endpoint: `GET /api/ecs-workspace/:workspaceName/connectivity/test`
- Tests connectivity to workspace container
- Validates service accessibility
- Returns comprehensive connectivity report

## Configuration Improvements

### Environment Variables
Services now properly bind to `0.0.0.0` instead of `localhost`:

```javascript
env: {
  ...process.env,
  PORT: port.toString(),
  HOST: '0.0.0.0',  // Ensures external accessibility
  NODE_ENV: 'development'
}
```

### Docker Configuration
Ports are properly exposed in the Dockerfile:
```dockerfile
EXPOSE 3000 8080 8000
```

### ECS Task Definition
All ports are mapped in the task definition:
```json
"portMappings": [
  { "containerPort": 3000, "protocol": "tcp" },
  { "containerPort": 8080, "protocol": "tcp" },
  { "containerPort": 8000, "protocol": "tcp" }
]
```

## Testing and Validation

### Automated Testing
1. Environment validation before service startup
2. Port accessibility testing during startup
3. Cross-service connectivity validation after startup
4. Periodic health checks

### Manual Testing
Use the new endpoints to manually test connectivity:

```bash
# Test basic connectivity
curl http://<public-ip>:3000/ping

# Get comprehensive health status
curl http://<public-ip>:3000/health

# Test service connectivity
curl http://<public-ip>:3000/api/connectivity/test

# Validate environment before startup
curl -X POST http://<public-ip>:3000/api/services/validate
```

## Monitoring and Diagnostics

### Enhanced Logging
- Detailed startup logs with timing information
- Connectivity test results
- Network interface information
- Service binding status

### Health Check Information
The `/health` endpoint now provides:
- Service status and port information
- Network interface details
- Connectivity test results
- Memory and system information

## Best Practices

1. **Always validate environment** before starting services
2. **Wait for actual port accessibility** before marking services as ready
3. **Test cross-service connectivity** after startup
4. **Use 0.0.0.0 binding** for external accessibility
5. **Implement retry mechanisms** for network operations
6. **Provide detailed error information** for debugging

## Future Improvements

1. **Service Discovery**: Implement proper service discovery mechanism
2. **Load Balancing**: Add load balancing for multiple service instances
3. **Circuit Breakers**: Implement circuit breakers for service communication
4. **Metrics Collection**: Add detailed metrics for connectivity monitoring
5. **Automated Recovery**: Implement automatic service restart on connectivity failures

## Troubleshooting

### Common Issues

1. **Port not accessible**: Check if service is binding to 0.0.0.0
2. **Service not starting**: Check environment validation results
3. **Cross-connectivity fails**: Verify both services are running and accessible
4. **Intermittent failures**: Check network interface configuration

### Debug Commands

```bash
# Check if ports are listening
netstat -tlnp | grep :8080
netstat -tlnp | grep :8000

# Test port connectivity
telnet <ip> 8080
telnet <ip> 8000

# Check service logs
docker logs <container-id>
```

This comprehensive solution ensures reliable connectivity between frontend and backend services in ECS workspaces, with proper validation, testing, and monitoring capabilities.
