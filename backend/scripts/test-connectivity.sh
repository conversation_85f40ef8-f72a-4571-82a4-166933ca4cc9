#!/bin/bash

# ECS Workspace Connectivity Test Script
# This script tests the connectivity improvements for ECS workspaces

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WORKSPACE_IP=${1:-"localhost"}
WORKSPACE_PORT=${2:-"3000"}
FRONTEND_PORT="8080"
BACKEND_PORT="8000"

echo -e "${BLUE}🔍 ECS Workspace Connectivity Test${NC}"
echo -e "${BLUE}====================================${NC}"
echo "Testing workspace at: ${WORKSPACE_IP}:${WORKSPACE_PORT}"
echo ""

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -n "Testing ${description}... "
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/response.json "${url}" 2>/dev/null); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            echo -e "${GREEN}✅ PASS${NC} (HTTP $status_code)"
            return 0
        else
            echo -e "${RED}❌ FAIL${NC} (HTTP $status_code, expected $expected_status)"
            return 1
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Connection failed)"
        return 1
    fi
}

# Function to test port connectivity
test_port() {
    local host=$1
    local port=$2
    local description=$3
    
    echo -n "Testing ${description} (${host}:${port})... "
    
    if timeout 5 bash -c "</dev/tcp/${host}/${port}" 2>/dev/null; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        return 1
    fi
}

# Test 1: Basic workspace connectivity
echo -e "${YELLOW}📡 Testing Basic Workspace Connectivity${NC}"
test_endpoint "http://${WORKSPACE_IP}:${WORKSPACE_PORT}/ping" "Workspace ping"
test_endpoint "http://${WORKSPACE_IP}:${WORKSPACE_PORT}/health" "Workspace health check"
echo ""

# Test 2: Service validation
echo -e "${YELLOW}🔧 Testing Service Validation${NC}"
test_endpoint "http://${WORKSPACE_IP}:${WORKSPACE_PORT}/api/services/validate" "Environment validation" "200"
echo ""

# Test 3: Port connectivity
echo -e "${YELLOW}🔌 Testing Port Connectivity${NC}"
test_port "${WORKSPACE_IP}" "${WORKSPACE_PORT}" "Workspace server port"
test_port "${WORKSPACE_IP}" "${FRONTEND_PORT}" "Frontend port (if running)"
test_port "${WORKSPACE_IP}" "${BACKEND_PORT}" "Backend port (if running)"
echo ""

# Test 4: Service connectivity test
echo -e "${YELLOW}🎯 Testing Service Connectivity${NC}"
test_endpoint "http://${WORKSPACE_IP}:${WORKSPACE_PORT}/api/connectivity/test" "Service connectivity test"
echo ""

# Test 5: Check health status details
echo -e "${YELLOW}📊 Checking Health Status Details${NC}"
if curl -s "http://${WORKSPACE_IP}:${WORKSPACE_PORT}/health" > /tmp/health.json 2>/dev/null; then
    echo "Health check response:"
    if command -v jq >/dev/null 2>&1; then
        cat /tmp/health.json | jq '.'
    else
        cat /tmp/health.json
    fi
else
    echo -e "${RED}❌ Failed to get health status${NC}"
fi
echo ""

# Test 6: Check connectivity test details
echo -e "${YELLOW}🔍 Checking Connectivity Test Details${NC}"
if curl -s "http://${WORKSPACE_IP}:${WORKSPACE_PORT}/api/connectivity/test" > /tmp/connectivity.json 2>/dev/null; then
    echo "Connectivity test response:"
    if command -v jq >/dev/null 2>&1; then
        cat /tmp/connectivity.json | jq '.'
    else
        cat /tmp/connectivity.json
    fi
else
    echo -e "${RED}❌ Failed to get connectivity test results${NC}"
fi
echo ""

# Summary
echo -e "${BLUE}📋 Test Summary${NC}"
echo -e "${BLUE}===============${NC}"

# Count successful tests (this is a simplified approach)
total_tests=6
echo "Total tests: ${total_tests}"
echo ""

echo -e "${GREEN}✅ Tests completed!${NC}"
echo ""
echo "If any tests failed, check:"
echo "1. Workspace container is running"
echo "2. Ports are properly exposed"
echo "3. Services are bound to 0.0.0.0"
echo "4. Network connectivity is working"
echo ""
echo "For detailed logs, check the workspace container logs:"
echo "docker logs <container-id>"

# Cleanup
rm -f /tmp/response.json /tmp/health.json /tmp/connectivity.json
