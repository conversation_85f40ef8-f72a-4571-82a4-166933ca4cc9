#!/bin/bash

# AWS ECS Infrastructure Deployment Script for Mergen Workspace
# This script sets up the complete AWS infrastructure for ECS-based workspaces

set -e

# Configuration
REGION=${AWS_REGION:-us-east-1}
VPC_CIDR="10.0.0.0/16"
SUBNET1_CIDR="********/24"
SUBNET2_CIDR="********/24"
CLUSTER_NAME="mergen-workspace-cluster"
ECR_REPO_NAME="mergen/workspace"

echo "🚀 Starting AWS ECS infrastructure deployment..."
echo "Region: $REGION"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI not configured. Please run 'aws configure' first."
    exit 1
fi

ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo "Account ID: $ACCOUNT_ID"

# 1. Get or Create VPC
echo "📡 Checking for existing VPC..."

# Try to find an existing VPC with our tag
VPC_ID=$(aws ec2 describe-vpcs \
    --filters "Name=tag:Name,Values=mergen-workspace-vpc" \
    --query 'Vpcs[0].VpcId' \
    --output text 2>/dev/null)

if [ "$VPC_ID" = "None" ] || [ -z "$VPC_ID" ]; then
    echo "No existing VPC found, checking for default VPC..."

    # Try to use default VPC
    VPC_ID=$(aws ec2 describe-vpcs \
        --filters "Name=is-default,Values=true" \
        --query 'Vpcs[0].VpcId' \
        --output text 2>/dev/null)

    if [ "$VPC_ID" = "None" ] || [ -z "$VPC_ID" ]; then
        echo "❌ No default VPC found and cannot create new VPC (limit reached)"
        echo "Please either:"
        echo "1. Delete an unused VPC to free up space"
        echo "2. Use an existing VPC by setting VPC_ID environment variable"
        echo "3. Run: export VPC_ID=vpc-xxxxxxxxx before running this script"
        exit 1
    else
        echo "Using default VPC: $VPC_ID"
    fi
else
    echo "Using existing mergen-workspace VPC: $VPC_ID"
fi

# Enable DNS hostnames if not already enabled
aws ec2 modify-vpc-attribute --vpc-id $VPC_ID --enable-dns-hostnames 2>/dev/null || echo "DNS hostnames already enabled"

# 2. Get or Create Internet Gateway
echo "🌐 Checking for existing Internet Gateway..."

# Check for existing IGW attached to our VPC
IGW_ID=$(aws ec2 describe-internet-gateways \
    --filters "Name=attachment.vpc-id,Values=$VPC_ID" \
    --query 'InternetGateways[0].InternetGatewayId' \
    --output text 2>/dev/null)

if [ "$IGW_ID" = "None" ] || [ -z "$IGW_ID" ]; then
    # Check for existing IGW with our tag
    IGW_ID=$(aws ec2 describe-internet-gateways \
        --filters "Name=tag:Name,Values=mergen-workspace-igw" \
        --query 'InternetGateways[0].InternetGatewayId' \
        --output text 2>/dev/null)

    if [ "$IGW_ID" = "None" ] || [ -z "$IGW_ID" ]; then
        echo "Creating Internet Gateway..."
        IGW_ID=$(aws ec2 create-internet-gateway \
            --tag-specifications 'ResourceType=internet-gateway,Tags=[{Key=Name,Value=mergen-workspace-igw}]' \
            --query 'InternetGateway.InternetGatewayId' \
            --output text)
        echo "Internet Gateway created: $IGW_ID"
    else
        echo "Using existing tagged Internet Gateway: $IGW_ID"
    fi

    # Attach Internet Gateway to VPC
    aws ec2 attach-internet-gateway --vpc-id $VPC_ID --internet-gateway-id $IGW_ID 2>/dev/null || echo "IGW already attached or attachment failed"
else
    echo "Using existing Internet Gateway attached to VPC: $IGW_ID"
fi

# 3. Get or Create Subnets
echo "🏗️ Checking for existing subnets..."

# Check for existing subnets
SUBNET1_ID=$(aws ec2 describe-subnets \
    --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=mergen-workspace-subnet-1" \
    --query 'Subnets[0].SubnetId' \
    --output text 2>/dev/null)

SUBNET2_ID=$(aws ec2 describe-subnets \
    --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=mergen-workspace-subnet-2" \
    --query 'Subnets[0].SubnetId' \
    --output text 2>/dev/null)

# Create subnet 1 if it doesn't exist
if [ "$SUBNET1_ID" = "None" ] || [ -z "$SUBNET1_ID" ]; then
    echo "Creating subnet 1..."
    SUBNET1_ID=$(aws ec2 create-subnet \
        --vpc-id $VPC_ID \
        --cidr-block $SUBNET1_CIDR \
        --availability-zone ${REGION}a \
        --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=mergen-workspace-subnet-1}]' \
        --query 'Subnet.SubnetId' \
        --output text)
    echo "Subnet 1 created: $SUBNET1_ID"

    # Enable auto-assign public IP
    aws ec2 modify-subnet-attribute --subnet-id $SUBNET1_ID --map-public-ip-on-launch
else
    echo "Using existing subnet 1: $SUBNET1_ID"
fi

# Create subnet 2 if it doesn't exist
if [ "$SUBNET2_ID" = "None" ] || [ -z "$SUBNET2_ID" ]; then
    echo "Creating subnet 2..."
    SUBNET2_ID=$(aws ec2 create-subnet \
        --vpc-id $VPC_ID \
        --cidr-block $SUBNET2_CIDR \
        --availability-zone ${REGION}b \
        --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=mergen-workspace-subnet-2}]' \
        --query 'Subnet.SubnetId' \
        --output text)
    echo "Subnet 2 created: $SUBNET2_ID"

    # Enable auto-assign public IP
    aws ec2 modify-subnet-attribute --subnet-id $SUBNET2_ID --map-public-ip-on-launch
else
    echo "Using existing subnet 2: $SUBNET2_ID"
fi

# 4. Get or Create Route Table
echo "🛣️ Checking for existing route table..."

# Check for existing route table
ROUTE_TABLE_ID=$(aws ec2 describe-route-tables \
    --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=mergen-workspace-rt" \
    --query 'RouteTables[0].RouteTableId' \
    --output text 2>/dev/null)

if [ "$ROUTE_TABLE_ID" = "None" ] || [ -z "$ROUTE_TABLE_ID" ]; then
    echo "Creating route table..."
    ROUTE_TABLE_ID=$(aws ec2 create-route-table \
        --vpc-id $VPC_ID \
        --tag-specifications 'ResourceType=route-table,Tags=[{Key=Name,Value=mergen-workspace-rt}]' \
        --query 'RouteTable.RouteTableId' \
        --output text)
    echo "Route table created: $ROUTE_TABLE_ID"

    # Add route to Internet Gateway
    aws ec2 create-route \
        --route-table-id $ROUTE_TABLE_ID \
        --destination-cidr-block 0.0.0.0/0 \
        --gateway-id $IGW_ID 2>/dev/null || echo "Route already exists or creation failed"

    # Associate subnets with route table
    aws ec2 associate-route-table --subnet-id $SUBNET1_ID --route-table-id $ROUTE_TABLE_ID 2>/dev/null || echo "Subnet 1 already associated or association failed"
    aws ec2 associate-route-table --subnet-id $SUBNET2_ID --route-table-id $ROUTE_TABLE_ID 2>/dev/null || echo "Subnet 2 already associated or association failed"
else
    echo "Using existing route table: $ROUTE_TABLE_ID"
fi

# 5. Get or Create Security Group
echo "🔒 Checking for existing security group..."

# Check for existing security group
SG_ID=$(aws ec2 describe-security-groups \
    --filters "Name=vpc-id,Values=$VPC_ID" "Name=group-name,Values=mergen-workspace-sg" \
    --query 'SecurityGroups[0].GroupId' \
    --output text 2>/dev/null)

if [ "$SG_ID" = "None" ] || [ -z "$SG_ID" ]; then
    echo "Creating security group..."
    SG_ID=$(aws ec2 create-security-group \
        --group-name mergen-workspace-sg \
        --description "Security group for Mergen workspace containers" \
        --vpc-id $VPC_ID \
        --tag-specifications 'ResourceType=security-group,Tags=[{Key=Name,Value=mergen-workspace-sg}]' \
        --query 'GroupId' \
        --output text)
    echo "Security group created: $SG_ID"

    # Add inbound rules
    aws ec2 authorize-security-group-ingress \
        --group-id $SG_ID \
        --protocol tcp \
        --port 3000 \
        --cidr 0.0.0.0/0 2>/dev/null || echo "Port 3000 rule already exists"

    aws ec2 authorize-security-group-ingress \
        --group-id $SG_ID \
        --protocol tcp \
        --port 8080 \
        --cidr 0.0.0.0/0 2>/dev/null || echo "Port 8080 rule already exists"

    aws ec2 authorize-security-group-ingress \
        --group-id $SG_ID \
        --protocol tcp \
        --port 8000 \
        --cidr 0.0.0.0/0 2>/dev/null || echo "Port 8000 rule already exists"

    aws ec2 authorize-security-group-ingress \
        --group-id $SG_ID \
        --protocol tcp \
        --port 2049 \
        --source-group $SG_ID 2>/dev/null || echo "EFS rule already exists"
else
    echo "Using existing security group: $SG_ID"
fi

# 6. Get or Create EFS File System
echo "📁 Checking for existing EFS file system..."

# Check for existing EFS file system
EFS_ID=$(aws efs describe-file-systems \
    --query 'FileSystems[?Tags[?Key==`Name` && Value==`mergen-workspace-efs`]].FileSystemId | [0]' \
    --output text 2>/dev/null)

if [ "$EFS_ID" = "None" ] || [ -z "$EFS_ID" ]; then
    echo "Creating EFS file system..."
    EFS_ID=$(aws efs create-file-system \
        --creation-token mergen-workspace-efs-$(date +%s) \
        --performance-mode generalPurpose \
        --throughput-mode provisioned \
        --provisioned-throughput-in-mibps 100 \
        --tags Key=Name,Value=mergen-workspace-efs \
        --query 'FileSystemId' \
        --output text)
    echo "EFS file system created: $EFS_ID"
else
    echo "Using existing EFS file system: $EFS_ID"
fi

# Check EFS status (but don't wait indefinitely)
echo "⏳ Checking EFS status..."
EFS_STATE=$(aws efs describe-file-systems --query "FileSystems[?FileSystemId==\`$EFS_ID\`].LifeCycleState | [0]" --output text 2>/dev/null)
echo "EFS state: $EFS_STATE"

if [ "$EFS_STATE" = "available" ]; then
    echo "✅ EFS is available"
elif [ "$EFS_STATE" = "creating" ]; then
    echo "⏳ EFS is still creating, but continuing with deployment..."
    echo "   Mount targets will be created when EFS becomes available"
else
    echo "⚠️ EFS state: $EFS_STATE - continuing anyway"
fi

# Create mount targets if they don't exist
echo "🔗 Checking for existing EFS mount targets..."

# Check for existing mount targets
EXISTING_MOUNT_TARGETS=$(aws efs describe-mount-targets \
    --file-system-id $EFS_ID \
    --query 'MountTargets[*].SubnetId' \
    --output text 2>/dev/null)

if [[ ! "$EXISTING_MOUNT_TARGETS" =~ "$SUBNET1_ID" ]]; then
    echo "Creating mount target for subnet 1..."
    aws efs create-mount-target \
        --file-system-id $EFS_ID \
        --subnet-id $SUBNET1_ID \
        --security-groups $SG_ID 2>/dev/null || echo "Mount target for subnet 1 already exists or creation failed"
else
    echo "Mount target for subnet 1 already exists"
fi

if [[ ! "$EXISTING_MOUNT_TARGETS" =~ "$SUBNET2_ID" ]]; then
    echo "Creating mount target for subnet 2..."
    aws efs create-mount-target \
        --file-system-id $EFS_ID \
        --subnet-id $SUBNET2_ID \
        --security-groups $SG_ID 2>/dev/null || echo "Mount target for subnet 2 already exists or creation failed"
else
    echo "Mount target for subnet 2 already exists"
fi

# Check for existing access point
echo "🎯 Checking for existing EFS access point..."
ACCESS_POINT_ID=$(aws efs describe-access-points \
    --file-system-id $EFS_ID \
    --query 'AccessPoints[?Tags[?Key==`Name` && Value==`mergen-workspace-access-point`]].AccessPointId' \
    --output text 2>/dev/null)

if [ "$ACCESS_POINT_ID" = "None" ] || [ -z "$ACCESS_POINT_ID" ]; then
    echo "Creating EFS access point..."
    ACCESS_POINT_ID=$(aws efs create-access-point \
        --file-system-id $EFS_ID \
        --posix-user Uid=1001,Gid=1001 \
        --root-directory Path=/workspaces,CreationInfo='{OwnerUid=1001,OwnerGid=1001,Permissions=755}' \
        --tags Key=Name,Value=mergen-workspace-access-point \
        --query 'AccessPointId' \
        --output text)
    echo "EFS access point created: $ACCESS_POINT_ID"
else
    echo "Using existing EFS access point: $ACCESS_POINT_ID"
fi

# 7. Create IAM Roles
echo "👤 Creating IAM roles..."

# ECS Task Execution Role
if aws iam get-role --role-name ecsTaskExecutionRole >/dev/null 2>&1; then
    echo "ecsTaskExecutionRole already exists, skipping creation"
else
    aws iam create-role \
        --role-name ecsTaskExecutionRole \
        --assume-role-policy-document file://aws-config/ecs-task-execution-role-policy.json \
        --tags Key=Name,Value=ecsTaskExecutionRole
    echo "ecsTaskExecutionRole created"
fi

aws iam attach-role-policy \
    --role-name ecsTaskExecutionRole \
    --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy 2>/dev/null || echo "Policy already attached"

# ECS Task Role
if aws iam get-role --role-name ecsTaskRole >/dev/null 2>&1; then
    echo "ecsTaskRole already exists, skipping creation"
else
    aws iam create-role \
        --role-name ecsTaskRole \
        --assume-role-policy-document file://aws-config/ecs-task-role-policy.json \
        --tags Key=Name,Value=ecsTaskRole
    echo "ecsTaskRole created"
fi

# Attach the correct EFS policy (AmazonElasticFileSystemClientWrite doesn't exist)
# We'll create a custom policy for EFS access
EFS_POLICY_ARN="arn:aws:iam::$ACCOUNT_ID:policy/ECSTaskEFSPolicy"

# Create custom EFS policy if it doesn't exist
if ! aws iam get-policy --policy-arn $EFS_POLICY_ARN >/dev/null 2>&1; then
    cat > /tmp/efs-policy.json << 'EOF'
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "elasticfilesystem:ClientMount",
                "elasticfilesystem:ClientWrite",
                "elasticfilesystem:ClientRootAccess"
            ],
            "Resource": "*"
        }
    ]
}
EOF

    aws iam create-policy \
        --policy-name ECSTaskEFSPolicy \
        --policy-document file:///tmp/efs-policy.json \
        --description "Policy for ECS tasks to access EFS"

    rm /tmp/efs-policy.json
    echo "Custom EFS policy created"
else
    echo "Custom EFS policy already exists"
fi

aws iam attach-role-policy \
    --role-name ecsTaskRole \
    --policy-arn $EFS_POLICY_ARN 2>/dev/null || echo "EFS policy already attached"

# 8. Get or Create ECR Repository
echo "📦 Checking for existing ECR repository..."
if ! aws ecr describe-repositories --repository-names $ECR_REPO_NAME >/dev/null 2>&1; then
    echo "Creating ECR repository..."
    aws ecr create-repository \
        --repository-name $ECR_REPO_NAME \
        --tags Key=Name,Value=mergen-workspace-repo
    echo "ECR repository created: $ECR_REPO_NAME"
else
    echo "Using existing ECR repository: $ECR_REPO_NAME"
fi

# 9. Get or Create ECS Cluster
echo "🏭 Checking for existing ECS cluster..."
if ! aws ecs describe-clusters --clusters $CLUSTER_NAME --query 'clusters[0].status' --output text 2>/dev/null | grep -q "ACTIVE"; then
    echo "Creating ECS cluster..."
    aws ecs create-cluster \
        --cluster-name $CLUSTER_NAME \
        --capacity-providers FARGATE \
        --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1 \
        --tags key=Name,value=$CLUSTER_NAME
    echo "ECS cluster created: $CLUSTER_NAME"
else
    echo "Using existing ECS cluster: $CLUSTER_NAME"
fi

# 10. Get or Create CloudWatch Log Group
echo "📊 Checking for existing CloudWatch log group..."
if ! aws logs describe-log-groups --log-group-name-prefix /ecs/mergen-workspace --query 'logGroups[0].logGroupName' --output text 2>/dev/null | grep -q "mergen-workspace"; then
    echo "Creating CloudWatch log group..."
    aws logs create-log-group --log-group-name /ecs/mergen-workspace

    # Set retention policy separately
    aws logs put-retention-policy \
        --log-group-name /ecs/mergen-workspace \
        --retention-in-days 7

    echo "CloudWatch log group created: /ecs/mergen-workspace"
else
    echo "Using existing CloudWatch log group: /ecs/mergen-workspace"
fi

# 11. Generate environment configuration
echo "⚙️ Generating environment configuration..."
cat > .env.aws << EOF
# AWS ECS Configuration - Generated by deployment script
AWS_REGION=$REGION
ECS_CLUSTER_NAME=$CLUSTER_NAME
ECS_EXECUTION_ROLE_ARN=arn:aws:iam::$ACCOUNT_ID:role/ecsTaskExecutionRole
ECS_TASK_ROLE_ARN=arn:aws:iam::$ACCOUNT_ID:role/ecsTaskRole
VPC_ID=$VPC_ID
SUBNET_IDS=$SUBNET1_ID,$SUBNET2_ID
SECURITY_GROUP_ID=$SG_ID
EFS_FILE_SYSTEM_ID=$EFS_ID
EFS_ACCESS_POINT_ID=$ACCESS_POINT_ID
CONTAINER_IMAGE=$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$ECR_REPO_NAME:latest
EOF

echo "✅ AWS ECS infrastructure deployment completed!"
echo ""
echo "📋 Summary:"
echo "  VPC ID: $VPC_ID"
echo "  Subnet IDs: $SUBNET1_ID, $SUBNET2_ID"
echo "  Security Group ID: $SG_ID"
echo "  EFS File System ID: $EFS_ID"
echo "  EFS Access Point ID: $ACCESS_POINT_ID"
echo "  ECS Cluster: $CLUSTER_NAME"
echo "  ECR Repository: $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$ECR_REPO_NAME"
echo ""
echo "📝 Next steps:"
echo "1. Copy the generated .env.aws configuration to your .env file"
echo "2. Build and push your container image to ECR"
echo "3. Update your application configuration"
echo "4. Test the deployment"
echo ""
echo "🐳 To build and push container image:"
echo "  cd docker/workspace"
echo "  docker build -t $ECR_REPO_NAME:latest ."
echo "  aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com"
echo "  docker tag $ECR_REPO_NAME:latest $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$ECR_REPO_NAME:latest"
echo "  docker push $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$ECR_REPO_NAME:latest"
